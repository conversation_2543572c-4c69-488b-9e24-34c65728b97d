<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>水果销量分布</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .chart-container {
            position: relative;
            height: 400px;
            width: 100%;
        }
        .data-table {
            margin-top: 30px;
            width: 100%;
            border-collapse: collapse;
        }
        .data-table th, .data-table td {
            padding: 10px;
            border: 1px solid #ddd;
            text-align: center;
        }
        .data-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>水果销量分布</h1>
        <div class="chart-container">
            <canvas id="pieChart"></canvas>
        </div>
        
        <table class="data-table">
            <thead>
                <tr>
                    <th>水果种类</th>
                    <th>销量占比</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>苹果</td>
                    <td>30%</td>
                </tr>
                <tr>
                    <td>香蕉</td>
                    <td>25%</td>
                </tr>
                <tr>
                    <td>橙子</td>
                    <td>20%</td>
                </tr>
                <tr>
                    <td>葡萄</td>
                    <td>15%</td>
                </tr>
                <tr>
                    <td>其他</td>
                    <td>10%</td>
                </tr>
            </tbody>
        </table>
    </div>

    <script>
        // 饼图数据
        const data = {
            labels: ['苹果', '香蕉', '橙子', '葡萄', '其他'],
            datasets: [{
                label: '销量占比',
                data: [30, 25, 20, 15, 10],
                backgroundColor: [
                    '#FF6B6B',  // 苹果 - 红色
                    '#FFE66D',  // 香蕉 - 黄色
                    '#FF8E53',  // 橙子 - 橙色
                    '#A8E6CF',  // 葡萄 - 绿色
                    '#88D8B0'   // 其他 - 浅绿色
                ],
                borderColor: '#ffffff',
                borderWidth: 2,
                hoverOffset: 4
            }]
        };

        // 饼图配置
        const config = {
            type: 'pie',
            data: data,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '水果销量分布',
                        font: {
                            size: 18,
                            weight: 'bold'
                        },
                        padding: {
                            top: 10,
                            bottom: 30
                        }
                    },
                    legend: {
                        position: 'right',
                        labels: {
                            font: {
                                size: 14
                            },
                            padding: 20,
                            usePointStyle: true
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.label + ': ' + context.parsed + '%';
                            }
                        }
                    }
                }
            }
        };

        // 创建饼图
        const ctx = document.getElementById('pieChart').getContext('2d');
        const pieChart = new Chart(ctx, config);
    </script>
</body>
</html>
