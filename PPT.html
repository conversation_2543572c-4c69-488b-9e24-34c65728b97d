
                        <!DOCTYPE html>
                        <html lang="en">
                        <head>
                            <meta charset="UTF-8">
                            <meta name="viewport" content="width=device-width, initial-scale=1.0">
							<style>
								body {
									background-color: white; /* Ensure the iframe has a white background */
								}

								
        /* =================================================================== */
        /* === 1. 全局设计变量 - 液态玻璃效果升级 === */
        /* =================================================================== */
        :root {
            /* 液态玻璃主题色彩 */
            --text-color: #1a1a1a;
            --heading-color: #0f0f0f;
            --accent-color: #00d4ff; /* 液态蓝色 */
            --accent-secondary: #ff6b9d; /* 液态粉色 */
            --accent-tertiary: #a855f7; /* 液态紫色 */
            --subtle-text-color: rgba(26, 26, 26, 0.85);

            /* 液态玻璃卡片变量 */
            --card-bg-color: rgba(255, 255, 255, 0.08);
            --card-bg-hover: rgba(255, 255, 255, 0.15);
            --card-border-color: rgba(255, 255, 255, 0.2);
            --card-border-hover: rgba(255, 255, 255, 0.35);
            --card-blur: 40px;
            --card-radius: 24px;
            --card-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            --card-shadow-hover: 0 20px 60px rgba(0, 0, 0, 0.2);

            /* 液态动画变量 */
            --liquid-speed: 8s;
            --liquid-delay: 0.3s;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html, body {
            width: 100%;
            height: 100%;
            overflow: hidden;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            color: var(--text-color);

            /* 液态玻璃背景 - 多层渐变营造深度感 */
            background:
                radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 107, 157, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(168, 85, 247, 0.1) 0%, transparent 50%),
                linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
            position: relative;
        }

        /* Modern geometric wave animation container */
        .wave-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }

        /* 液态玻璃波浪层 - 更流畅的动态效果 */
        .wave {
            position: absolute;
            width: 100%;
            height: 100%;
            animation-iteration-count: infinite;
            animation-timing-function: cubic-bezier(0.4, 0, 0.6, 1);
            will-change: transform, opacity;
        }

        .wave1 {
            background:
                radial-gradient(ellipse 800px 600px at 10% 90%, rgba(0, 212, 255, 0.2) 0%, transparent 60%),
                linear-gradient(45deg, rgba(0, 212, 255, 0.08) 0%, transparent 70%);
            animation: liquidWave1 var(--liquid-speed) ease-in-out infinite;
            transform-origin: center center;
            filter: blur(1px);
        }

        .wave2 {
            background:
                radial-gradient(ellipse 600px 800px at 90% 10%, rgba(255, 107, 157, 0.18) 0%, transparent 65%),
                linear-gradient(-45deg, rgba(255, 107, 157, 0.06) 0%, transparent 75%);
            animation: liquidWave2 calc(var(--liquid-speed) * 1.3) ease-in-out infinite;
            animation-delay: calc(var(--liquid-delay) * 1);
            transform-origin: center center;
            filter: blur(1.5px);
        }

        .wave3 {
            background:
                radial-gradient(ellipse 700px 500px at 50% 80%, rgba(168, 85, 247, 0.15) 0%, transparent 70%),
                conic-gradient(from 45deg at 30% 70%, rgba(168, 85, 247, 0.1) 0deg, transparent 180deg, rgba(168, 85, 247, 0.05) 360deg);
            animation: liquidWave3 calc(var(--liquid-speed) * 1.8) ease-in-out infinite;
            animation-delay: calc(var(--liquid-delay) * 2);
            transform-origin: 30% 70%;
            filter: blur(2px);
        }

        .wave4 {
            background:
                radial-gradient(ellipse 500px 700px at 80% 20%, rgba(255, 107, 157, 0.12) 0%, transparent 80%),
                linear-gradient(135deg, rgba(255, 107, 157, 0.04) 0%, transparent 60%);
            animation: liquidWave4 calc(var(--liquid-speed) * 0.8) ease-in-out infinite;
            animation-delay: calc(var(--liquid-delay) * 3);
            transform-origin: 80% 20%;
            filter: blur(1.2px);
        }

        .wave5 {
            background:
                radial-gradient(ellipse 900px 400px at 60% 30%, rgba(0, 212, 255, 0.1) 0%, transparent 85%),
                linear-gradient(90deg, rgba(0, 212, 255, 0.03) 0%, rgba(168, 85, 247, 0.03) 50%, transparent 100%);
            animation: liquidWave5 calc(var(--liquid-speed) * 2.2) ease-in-out infinite;
            animation-delay: calc(var(--liquid-delay) * 4);
            transform-origin: center center;
            filter: blur(0.8px);
        }

        /* 液态玻璃动画 - 更自然的流体运动 */
        @keyframes liquidWave1 {
            0%, 100% {
                transform: rotate(0deg) scale(1) translate(0, 0);
                opacity: 0.8;
            }
            25% {
                transform: rotate(3deg) scale(1.1) translate(30px, -20px);
                opacity: 1;
            }
            50% {
                transform: rotate(-2deg) scale(0.9) translate(-15px, 25px);
                opacity: 0.7;
            }
            75% {
                transform: rotate(4deg) scale(1.05) translate(20px, -10px);
                opacity: 0.9;
            }
        }

        @keyframes liquidWave2 {
            0%, 100% {
                transform: rotate(0deg) scale(1) translate(0, 0);
                opacity: 0.6;
            }
            30% {
                transform: rotate(-4deg) scale(1.15) translate(-25px, -30px);
                opacity: 0.9;
            }
            70% {
                transform: rotate(2deg) scale(0.85) translate(35px, 15px);
                opacity: 0.7;
            }
        }

        @keyframes liquidWave3 {
            0%, 100% {
                transform: scale(1) rotate(0deg) translate(0, 0);
                opacity: 0.5;
            }
            33% {
                transform: scale(1.2) rotate(120deg) translate(10px, -20px);
                opacity: 0.8;
            }
            66% {
                transform: scale(0.8) rotate(240deg) translate(-15px, 25px);
                opacity: 0.6;
            }
        }

        @keyframes liquidWave4 {
            0%, 100% {
                transform: scale(1) rotate(0deg) translate(0, 0);
                opacity: 0.4;
            }
            40% {
                transform: scale(1.3) rotate(-90deg) translate(20px, 30px);
                opacity: 0.7;
            }
            80% {
                transform: scale(0.9) rotate(-180deg) translate(-10px, -15px);
                opacity: 0.5;
            }
        }

        @keyframes liquidWave5 {
            0%, 100% {
                transform: translate(0, 0) scale(1) rotate(0deg);
                opacity: 0.3;
            }
            20% {
                transform: translate(40px, -25px) scale(1.1) rotate(15deg);
                opacity: 0.6;
            }
            40% {
                transform: translate(-30px, 35px) scale(0.9) rotate(-10deg);
                opacity: 0.4;
            }
            60% {
                transform: translate(25px, -15px) scale(1.05) rotate(20deg);
                opacity: 0.7;
            }
            80% {
                transform: translate(-20px, 20px) scale(0.95) rotate(-5deg);
                opacity: 0.5;
            }
        }
        
        /* Main layout and transitions remain the same */
        .main {
            width: 100%;
            height: 100%;
            display: flex;
            transition: transform 1s cubic-bezier(0.86, 0, 0.07, 1);
        }

        .page {
            width: 100vw;
            height: 100vh;
            flex-shrink: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 0 5vw;
        }
        
        .page-inner {
            width: 100%;
            max-width: 1200px;
            text-align: center;
            opacity: 0;
            transform: translateY(50px) scale(0.95);
            transition: none;
            filter: blur(10px);
        }
        .page.active .page-inner {
            opacity: 1;
            transform: translateY(0) scale(1);
            filter: blur(0px);
            transition:
                opacity 1s cubic-bezier(0.4, 0, 0.2, 1) 0.3s,
                transform 1s cubic-bezier(0.4, 0, 0.2, 1) 0.3s,
                filter 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.5s;
        }

        /* 液态玻璃页面切换效果 */
        .page {
            position: relative;
        }

        .page::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.1) 0%,
                rgba(255, 255, 255, 0.05) 50%,
                rgba(255, 255, 255, 0.1) 100%);
            opacity: 0;
            transition: opacity 0.6s ease;
            pointer-events: none;
            z-index: 1;
        }

        .page.active::before {
            opacity: 1;
        }
        
        /* =================================================================== */
        /* === 2. Card Design (已根据参考文件更新) === */
        /* =================================================================== */

        /* 液态玻璃卡片样式 - 增强的玻璃形态效果 */
        .card, .grid-item {
            background: var(--card-bg-color);
            backdrop-filter: blur(var(--card-blur)) saturate(200%) brightness(110%);
            -webkit-backdrop-filter: blur(var(--card-blur)) saturate(200%) brightness(110%);
            border-radius: var(--card-radius);
            border: 1px solid var(--card-border-color);
            box-shadow: var(--card-shadow);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        /* 液态玻璃反光效果 */
        .card::before, .grid-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent 0%,
                rgba(255, 255, 255, 0.4) 50%,
                transparent 100%);
            transition: left 0.6s ease;
            z-index: 1;
        }

        .card {
            display: flex;
            align-items: center;
            text-align: left;
            padding: 1.5rem 2rem;
        }

        /* 液态玻璃悬停效果 - 更动态的交互 */
        .card:hover, .grid-item:hover {
            transform: translateY(-12px) scale(1.02);
            background: var(--card-bg-hover);
            border-color: var(--card-border-hover);
            box-shadow: var(--card-shadow-hover);
        }

        .card:hover::before, .grid-item:hover::before {
            left: 100%;
        }

        /* All other styles remain largely the same, but adapted for the new theme */
        .content-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; width: 100%; }
        .grid-item { padding: 2rem; }
        
        /* 液态玻璃文字效果 */
        h1 {
            font-size: 4rem;
            font-weight: 700;
            color: var(--heading-color);
            margin-bottom: 1rem;
            background: linear-gradient(135deg, var(--heading-color) 0%, var(--accent-color) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            position: relative;
        }

        h1::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 120%;
            height: 120%;
            background: radial-gradient(ellipse, rgba(0, 212, 255, 0.1) 0%, transparent 70%);
            border-radius: 50%;
            z-index: -1;
            animation: liquidGlow 4s ease-in-out infinite;
        }

        @keyframes liquidGlow {
            0%, 100% {
                opacity: 0.3;
                transform: translate(-50%, -50%) scale(1);
            }
            50% {
                opacity: 0.6;
                transform: translate(-50%, -50%) scale(1.1);
            }
        }

        h2 {
            font-size: 3rem;
            font-weight: 600;
            color: var(--heading-color);
            margin-bottom: 3rem;
            display: inline-block;
            border-bottom: 3px solid;
            border-image: linear-gradient(90deg, var(--accent-color), var(--accent-secondary)) 1;
            padding-bottom: 1rem;
            position: relative;
        }

        h2::after {
            content: '';
            position: absolute;
            bottom: -3px;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, var(--accent-color), var(--accent-secondary));
            border-radius: 2px;
            box-shadow: 0 2px 8px rgba(0, 212, 255, 0.3);
        }

        h3 {
            font-size: 1.6rem;
            font-weight: 600;
            color: var(--heading-color);
            margin-bottom: 1rem;
        }

        p {
            font-size: 1.4rem;
            line-height: 1.8;
            color: var(--subtle-text-color);
        }

        .subtitle {
            font-size: 1.6rem;
            color: var(--subtle-text-color);
            font-weight: 400;
        }
        
        .card-list {
            list-style: none;
            display: flex;
            flex-direction: column;
            gap: 1.8rem;
            width: 100%;
            max-width: 900px;
            margin: 0 auto;
        }

        .card-content {
            font-size: 1.4rem;
            line-height: 1.8;
            color: var(--subtle-text-color);
            position: relative;
            z-index: 2;
        }

        /* 液态玻璃卡片动画 */
        .card, .grid-item {
            animation: liquidFloat 6s ease-in-out infinite;
        }

        .card:nth-child(2n) {
            animation-delay: 1s;
        }

        .card:nth-child(3n) {
            animation-delay: 2s;
        }

        .grid-item:nth-child(2n) {
            animation-delay: 1.5s;
        }

        .grid-item:nth-child(3n) {
            animation-delay: 3s;
        }

        @keyframes liquidFloat {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-5px);
            }
        }

        /* 液态玻璃内容进入动画 */
        .page.active .card {
            animation: liquidSlideIn 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        }

        .page.active .card:nth-child(1) { animation-delay: 0.1s; }
        .page.active .card:nth-child(2) { animation-delay: 0.2s; }
        .page.active .card:nth-child(3) { animation-delay: 0.3s; }
        .page.active .card:nth-child(4) { animation-delay: 0.4s; }

        .page.active .grid-item {
            animation: liquidSlideIn 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        }

        .page.active .grid-item:nth-child(1) { animation-delay: 0.2s; }
        .page.active .grid-item:nth-child(2) { animation-delay: 0.4s; }
        .page.active .grid-item:nth-child(3) { animation-delay: 0.6s; }

        @keyframes liquidSlideIn {
            0% {
                opacity: 0;
                transform: translateY(30px) scale(0.9);
                filter: blur(5px);
            }
            100% {
                opacity: 1;
                transform: translateY(0) scale(1);
                filter: blur(0px);
            }
        }
        /* 液态玻璃图标和高亮效果 */
        .card i.fas {
            background: linear-gradient(135deg, var(--accent-color), var(--accent-secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 1.8rem;
            margin-right: 1.5rem;
            width: 35px;
            text-align: center;
            filter: drop-shadow(0 2px 4px rgba(0, 212, 255, 0.3));
        }

        .highlight {
            background: linear-gradient(135deg, var(--accent-color), var(--accent-secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 600;
        }

        .grid-item i.fas {
            font-size: 2.5rem;
            margin: 0 0 1rem 0;
            display: block;
            width: auto;
            background: linear-gradient(135deg, var(--accent-color), var(--accent-tertiary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            filter: drop-shadow(0 4px 8px rgba(0, 212, 255, 0.4));
        }
        
        /* 液态玻璃警告框 */
        .warning-box {
            background: linear-gradient(135deg, var(--card-bg-color), var(--card-bg-hover));
            color: var(--text-color);
            backdrop-filter: blur(var(--card-blur)) saturate(180%);
            -webkit-backdrop-filter: blur(var(--card-blur)) saturate(180%);
            border: 2px solid;
            border-image: linear-gradient(135deg, var(--accent-color), var(--accent-secondary)) 1;
            padding: 0.8rem 1.5rem;
            border-radius: 12px;
            font-weight: 700;
            display: inline-flex;
            align-items: center;
            margin-top: 1rem;
            box-shadow: 0 4px 16px rgba(0, 212, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .warning-box::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(255, 107, 157, 0.1));
            z-index: -1;
        }

        .warning-box i {
            margin-right: 10px;
            background: linear-gradient(135deg, var(--accent-color), var(--accent-secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* 液态玻璃导航元素 */
        .pagination {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            list-style: none;
            z-index: 1000;
            display: flex;
            gap: 15px;
            padding: 12px 20px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px) saturate(180%);
            -webkit-backdrop-filter: blur(20px) saturate(180%);
            border-radius: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .pagination-item {
            width: 14px;
            height: 14px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
        }

        .pagination-item::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 0;
            height: 0;
            background: linear-gradient(135deg, var(--accent-color), var(--accent-secondary));
            border-radius: 50%;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .pagination-item.active {
            transform: scale(1.3);
            background: rgba(255, 255, 255, 0.2);
        }

        .pagination-item.active::before {
            width: 100%;
            height: 100%;
        }

        .nav-button {
            position: fixed;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.08);
            color: var(--text-color);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            width: 56px;
            height: 56px;
            font-size: 20px;
            cursor: pointer;
            z-index: 100;
            display: flex;
            justify-content: center;
            align-items: center;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            opacity: 0.7;
            backdrop-filter: blur(20px) saturate(180%);
            -webkit-backdrop-filter: blur(20px) saturate(180%);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }

        .nav-button:hover {
            background: rgba(255, 255, 255, 0.15);
            opacity: 1;
            transform: translateY(-50%) scale(1.1);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        }

        .nav-button.hidden {
            opacity: 0;
            pointer-events: none;
            transform: translateY(-50%) scale(0.8);
        }

        #prev-btn { left: 30px; }
        #next-btn { right: 30px; }

        /* 液态玻璃粒子效果 */
        .liquid-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
            overflow: hidden;
        }

        .particle {
            position: absolute;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
            backdrop-filter: blur(2px);
            -webkit-backdrop-filter: blur(2px);
            animation-iteration-count: infinite;
            animation-timing-function: linear;
        }

        .particle-1 {
            width: 4px;
            height: 4px;
            top: 20%;
            left: 10%;
            animation: particleFloat1 15s infinite;
        }

        .particle-2 {
            width: 6px;
            height: 6px;
            top: 60%;
            left: 80%;
            animation: particleFloat2 20s infinite;
        }

        .particle-3 {
            width: 3px;
            height: 3px;
            top: 30%;
            left: 70%;
            animation: particleFloat3 18s infinite;
        }

        .particle-4 {
            width: 5px;
            height: 5px;
            top: 80%;
            left: 20%;
            animation: particleFloat4 22s infinite;
        }

        .particle-5 {
            width: 4px;
            height: 4px;
            top: 40%;
            left: 50%;
            animation: particleFloat5 16s infinite;
        }

        .particle-6 {
            width: 7px;
            height: 7px;
            top: 70%;
            left: 40%;
            animation: particleFloat6 25s infinite;
        }

        @keyframes particleFloat1 {
            0%, 100% { transform: translate(0, 0) rotate(0deg); opacity: 0.3; }
            25% { transform: translate(30px, -40px) rotate(90deg); opacity: 0.7; }
            50% { transform: translate(-20px, -80px) rotate(180deg); opacity: 0.5; }
            75% { transform: translate(40px, -60px) rotate(270deg); opacity: 0.8; }
        }

        @keyframes particleFloat2 {
            0%, 100% { transform: translate(0, 0) rotate(0deg); opacity: 0.4; }
            33% { transform: translate(-50px, 30px) rotate(120deg); opacity: 0.8; }
            66% { transform: translate(25px, -45px) rotate(240deg); opacity: 0.6; }
        }

        @keyframes particleFloat3 {
            0%, 100% { transform: translate(0, 0) rotate(0deg); opacity: 0.2; }
            50% { transform: translate(-35px, 60px) rotate(180deg); opacity: 0.6; }
        }

        @keyframes particleFloat4 {
            0%, 100% { transform: translate(0, 0) rotate(0deg); opacity: 0.5; }
            25% { transform: translate(45px, -25px) rotate(90deg); opacity: 0.3; }
            50% { transform: translate(-30px, -50px) rotate(180deg); opacity: 0.7; }
            75% { transform: translate(20px, -35px) rotate(270deg); opacity: 0.4; }
        }

        @keyframes particleFloat5 {
            0%, 100% { transform: translate(0, 0) rotate(0deg); opacity: 0.3; }
            40% { transform: translate(-40px, -30px) rotate(144deg); opacity: 0.8; }
            80% { transform: translate(35px, 45px) rotate(288deg); opacity: 0.5; }
        }

        @keyframes particleFloat6 {
            0%, 100% { transform: translate(0, 0) rotate(0deg); opacity: 0.4; }
            20% { transform: translate(25px, -55px) rotate(72deg); opacity: 0.7; }
            40% { transform: translate(-45px, -30px) rotate(144deg); opacity: 0.3; }
            60% { transform: translate(-20px, 40px) rotate(216deg); opacity: 0.8; }
            80% { transform: translate(50px, 20px) rotate(288deg); opacity: 0.5; }
        }



							</style>
                        </head>
                        <body>
                            <!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=1920, height=1080, initial-scale=1.0">
    <title>AI+Python提高工作效率 - Frosted Glass Edition</title>
    <!-- 引入Font Awesome图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        /* =================================================================== */
        /* === 1. 全局设计变量 (已根据参考文件更新) === */
        /* =================================================================== */
        :root {
            /* 液态玻璃主题色彩 */
            --text-color: #1a1a1a;
            --heading-color: #0f0f0f;
            --accent-color: #00d4ff; /* 液态蓝色 */
            --accent-secondary: #ff6b9d; /* 液态粉色 */
            --accent-tertiary: #a855f7; /* 液态紫色 */
            --subtle-text-color: rgba(26, 26, 26, 0.85);

            /* 液态玻璃卡片变量 */
            --card-bg-color: rgba(255, 255, 255, 0.08);
            --card-bg-hover: rgba(255, 255, 255, 0.15);
            --card-border-color: rgba(255, 255, 255, 0.2);
            --card-border-hover: rgba(255, 255, 255, 0.35);
            --card-blur: 40px;
            --card-radius: 24px;
            --card-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            --card-shadow-hover: 0 20px 60px rgba(0, 0, 0, 0.2);

            /* 液态动画变量 */
            --liquid-speed: 8s;
            --liquid-delay: 0.3s;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html, body {
            width: 100%;
            height: 100%;
            overflow: hidden;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            color: var(--text-color);

            /* 液态玻璃背景 - 多层渐变营造深度感 */
            background:
                radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 107, 157, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(168, 85, 247, 0.1) 0%, transparent 50%),
                linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
            position: relative;
        }
        
        /* Main layout and transitions remain the same */
        .main {
            width: 100%;
            height: 100%;
            display: flex;
            transition: transform 1s cubic-bezier(0.86, 0, 0.07, 1);
        }

        .page {
            width: 100vw;
            height: 100vh;
            flex-shrink: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 0 5vw;
        }
        
        .page-inner {
            width: 100%;
            max-width: 1200px;
            text-align: center;
            opacity: 0;
            transform: translateY(30px);
            transition: none;
        }
        .page.active .page-inner {
            opacity: 1;
            transform: translateY(0);
            transition: opacity 0.8s ease-out 0.4s, transform 0.8s ease-out 0.4s;
        }
        
        /* =================================================================== */
        /* === 2. Card Design (已根据参考文件更新) === */
        /* =================================================================== */

        /* 液态玻璃卡片样式 - 增强的玻璃形态效果 */
        .card, .grid-item {
            background: var(--card-bg-color);
            backdrop-filter: blur(var(--card-blur)) saturate(200%) brightness(110%);
            -webkit-backdrop-filter: blur(var(--card-blur)) saturate(200%) brightness(110%);
            border-radius: var(--card-radius);
            border: 1px solid var(--card-border-color);
            box-shadow: var(--card-shadow);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        /* 液态玻璃反光效果 */
        .card::before, .grid-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent 0%,
                rgba(255, 255, 255, 0.4) 50%,
                transparent 100%);
            transition: left 0.6s ease;
            z-index: 1;
        }

        .card {
            display: flex;
            align-items: center;
            text-align: left;
            padding: 1.5rem 2rem;
        }

        /* 液态玻璃悬停效果 - 更动态的交互 */
        .card:hover, .grid-item:hover {
            transform: translateY(-12px) scale(1.02);
            background: var(--card-bg-hover);
            border-color: var(--card-border-hover);
            box-shadow: var(--card-shadow-hover);
        }

        .card:hover::before, .grid-item:hover::before {
            left: 100%;
        }

        /* All other styles remain largely the same, but adapted for the new theme */
        .content-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; width: 100%; }
        .grid-item { padding: 2rem; }
        
        /* 液态玻璃文字效果 */
        h1 {
            font-size: 4rem;
            font-weight: 700;
            color: var(--heading-color);
            margin-bottom: 1rem;
            background: linear-gradient(135deg, var(--heading-color) 0%, var(--accent-color) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            position: relative;
        }

        h1::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 120%;
            height: 120%;
            background: radial-gradient(ellipse, rgba(0, 212, 255, 0.1) 0%, transparent 70%);
            border-radius: 50%;
            z-index: -1;
            animation: liquidGlow 4s ease-in-out infinite;
        }

        @keyframes liquidGlow {
            0%, 100% {
                opacity: 0.3;
                transform: translate(-50%, -50%) scale(1);
            }
            50% {
                opacity: 0.6;
                transform: translate(-50%, -50%) scale(1.1);
            }
        }

        h2 {
            font-size: 3rem;
            font-weight: 600;
            color: var(--heading-color);
            margin-bottom: 3rem;
            display: inline-block;
            border-bottom: 3px solid;
            border-image: linear-gradient(90deg, var(--accent-color), var(--accent-secondary)) 1;
            padding-bottom: 1rem;
            position: relative;
        }

        h2::after {
            content: '';
            position: absolute;
            bottom: -3px;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, var(--accent-color), var(--accent-secondary));
            border-radius: 2px;
            box-shadow: 0 2px 8px rgba(0, 212, 255, 0.3);
        }

        h3 {
            font-size: 1.6rem;
            font-weight: 600;
            color: var(--heading-color);
            margin-bottom: 1rem;
        }

        p {
            font-size: 1.4rem;
            line-height: 1.8;
            color: var(--subtle-text-color);
        }

        .subtitle {
            font-size: 1.6rem;
            color: var(--subtle-text-color);
            font-weight: 400;
        }
        
        .card-list { list-style: none; display: flex; flex-direction: column; gap: 1.5rem; width: 100%; max-width: 900px; margin: 0 auto; }
        .card-content { font-size: 1.4rem; line-height: 1.7; color: var(--subtle-text-color); }
        .card i.fas { color: var(--accent-color); font-size: 1.8rem; margin-right: 1.5rem; width: 35px; text-align: center; }
        .highlight { color: var(--accent-color); font-weight: 500; }
        
        .grid-item i.fas { font-size: 2.5rem; margin: 0 0 1rem 0; display: block; width: auto; color: var(--accent-color); }
        
        .warning-box { background-color: var(--card-bg-color); color: var(--text-color); backdrop-filter: blur(var(--card-blur)); border: 1px solid var(--accent-color); padding: 0.8rem 1.5rem; border-radius: 8px; font-weight: 700; display: inline-flex; align-items: center; margin-top: 1rem; }
        .warning-box i { margin-right: 10px; color: var(--accent-color); }

        /* Navigation elements remain the same */
        .pagination { position: fixed; bottom: 30px; left: 50%; transform: translateX(-50%); list-style: none; z-index: 1000; display: flex; gap: 15px; padding: 8px 15px; background: rgba(0, 0, 0, 0.2); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); border-radius: 20px; }
        .pagination-item { width: 12px; height: 12px; border-radius: 50%; background-color: rgba(255, 255, 255, 0.4); cursor: pointer; transition: background-color 0.3s, transform 0.3s; }
        .pagination-item.active { background-color: var(--accent-color); transform: scale(1.4); }
        .nav-button { position: fixed; top: 50%; transform: translateY(-50%); background-color: rgba(255, 255, 255, 0.1); color: white; border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 50%; width: 50px; height: 50px; font-size: 20px; cursor: pointer; z-index: 100; display: flex; justify-content: center; align-items: center; transition: background-color 0.3s, opacity 0.3s; opacity: 0.7; backdrop-filter: blur(5px); -webkit-backdrop-filter: blur(5px); }
        .nav-button:hover { background-color: rgba(255, 255, 255, 0.2); opacity: 1; }
        .nav-button.hidden { opacity: 0; pointer-events: none; }
        #prev-btn { left: 30px; }
        #next-btn { right: 30px; }

    </style>
</head>
<body>
    <!-- 液态玻璃背景容器 -->
    <div class="wave-container">
        <div class="wave wave1"></div>
        <div class="wave wave2"></div>
        <div class="wave wave3"></div>
        <div class="wave wave4"></div>
        <div class="wave wave5"></div>
    </div>

    <!-- 液态玻璃粒子效果 -->
    <div class="liquid-particles">
        <div class="particle particle-1"></div>
        <div class="particle particle-2"></div>
        <div class="particle particle-3"></div>
        <div class="particle particle-4"></div>
        <div class="particle particle-5"></div>
        <div class="particle particle-6"></div>
    </div>

    <main class="main">
        <!-- The HTML content of all 9 pages remains exactly the same -->
        <!-- Slide 1: 封面页 -->
        <div class="page">
            <div class="page-inner">
                <h1>利用AI+Python实现工作效能新飞跃</h1>
                <p class="subtitle">拥抱变革</p>
                <p class="subtitle">演讲人：林东</p>
            </div>
        </div>
        <!-- Slide 2: 挑战与机遇 -->
        <div class="page">
            <div class="page-inner">
                <h2>挑战与机遇</h2>
                <ul class="card-list">
                    <li class="card"><i class="fas fa-cogs"></i><div class="card-content"><span class="highlight">当前痛点：</span>日常数据处理任务繁琐、重复。</div></li>
                    <li class="card"><i class="fas fa-exclamation-triangle"></i><div class="card-content"><span class="highlight">潜在风险：</span>耗费大量时间，且人工操作容易出错。</div></li>
                    <li class="card"><i class="fas fa-lightbulb"></i><div class="card-content"><span class="highlight">核心方案：</span>利用AI辅助，自动化完成Python编程。</div></li>
                    <li class="card"><i class="fas fa-rocket"></i><div class="card-content"><span class="highlight">未来机遇：</span>将我们从重复劳动中解放，聚焦更高价值的工作。</div></li>
                </ul>
            </div>
        </div>
        <!-- Slide 3: 什么是Python -->
        <div class="page">
            <div class="page-inner">
                <h2>Part 1 - 什么是Python？</h2>
                <ul class="card-list">
                    <li class="card"><i class="fas fa-code"></i><div class="card-content"><span class="highlight">强大工具：</span>一种功能强大的高级编程语言。</div></li>
                    <li class="card"><i class="fas fa-sitemap"></i><div class="card-content"><span class="highlight">应用广泛：</span>在数据分析、办公自动化、AI等领域是绝对主力。</div></li>
                    <li class="card"><i class="fas fa-book-open"></i><div class="card-content"><span class="highlight">学习成本：</span>传统方式下存在一定学习门槛，但AI正在改变这一切！</div></li>
                </ul>
            </div>
        </div>
        <!-- Slide 4: 痛点聚焦 -->
        <div class="page">
            <div class="page-inner">
                <h2>Part 2 - 痛点聚焦：数据处理</h2>
                <ul class="card-list">
                    <li class="card"><i class="fas fa-spinner fa-spin"></i><div class="card-content"><span class="highlight">繁琐耗时：</span>海量的手动复制、粘贴、核对操作。</div></li>
                    <li class="card"><i class="fas fa-times-circle"></i><div class="card-content"><span class="highlight">容易出错：</span>人工操作难以保证100%的准确性。</div></li>
                    <li class="card"><i class="fas fa-battery-quarter"></i><div class="card-content"><span class="highlight">效率低下：</span>占用了本可用于创造性工作的大量宝贵时间。</div></li>
                </ul>
            </div>
        </div>
        <!-- Slide 5: 核心解决方案 -->
        <div class="page">
            <div class="page-inner">
                <h2>Part 3 - 核心解决方案</h2>
                <p class="subtitle" style="margin-bottom: 2rem;">让AI为我们写代码</p>
                <div class="content-grid">
                    <div class="grid-item">
                        <h3>Before: 手动操作</h3>
                        <p>数小时甚至数天</p>
                        <p>易错、枯燥、效率低</p>
                    </div>
                    <div class="grid-item">
                        <h3>After: AI + Python</h3>
                        <p>几十秒代码运行</p>
                        <p>精准、高效、可复用</p>
                    </div>
                </div>
            </div>
        </div>
        <!-- Slide 6: 如何“指挥”AI -->
        <div class="page">
             <div class="page-inner">
                <h2>如何“指挥”AI？三步搞定！</h2>
                <div class="content-grid">
                    <div class="grid-item">
                        <i class="fas fa-bullseye"></i>
                        <h3>第一步：明确需求</h3>
                        <p>使用清晰的提示词描述任务，必要时上传脱敏后的文件。</p>
                    </div>
                    <div class="grid-item">
                        <i class="fas fa-comments"></i>
                        <h3>第二步：沟通迭代</h3>
                        <p>将报错信息反馈给AI，与AI反复沟通，修正Bug。</p>
                    </div>
                    <div class="grid-item">
                        <i class="fas fa-shield-alt"></i>
                        <h3>第三步：安全第一</h3>
                        <p class="warning-box"><i class="fas fa-exclamation-circle"></i>敏感数据请务必脱敏</p>
                    </div>
                </div>
            </div>
        </div>
        <!-- Slide 7: 更多可能性 -->
        <div class="page">
            <div class="page-inner">
                <h2>Part 4 - AI赋能的更多可能性</h2>
                <div class="content-grid">
                    <div class="grid-item">
                        <i class="fas fa-chart-pie"></i>
                        <h3>数据可视化</h3>
                        <p>让AI自动生成专业的分析图表，让汇报更清晰有力。</p>
                    </div>
                    <div class="grid-item">
                        <i class="fas fa-desktop"></i>
                        <h3>创意演示</h3>
                        <p>让AI编写HTML代码，制作比传统PPT更生动的动态演示。</p>
                    </div>
                    <div class="grid-item">
                        <i class="fas fa-database"></i>
                        <h3>知识管理</h3>
                        <p>用AI搭建专属知识库，实现法规、资料的秒级快速查询。</p>
                    </div>
                </div>
            </div>
        </div>
        <!-- Slide 8: 总结与展望 -->
        <div class="page">
            <div class="page-inner">
                <h2>总结：开启高效工作新模式</h2>
                <ul class="card-list">
                    <li class="card"><i class="fas fa-key"></i><div class="card-content"><span class="highlight">一把“金钥匙”：</span>AI + Python 是解放生产力的强大工具。</div></li>
                    <li class="card"><i class="fas fa-user-astronaut"></i><div class="card-content"><span class="highlight">一个身份转变：</span>从“执行者”转变为智能工具的“指挥者”。</div></li>
                    <li class="card"><i class="fas fa-forward"></i><div class="card-content"><span class="highlight">共同的未来：</span>拥抱新技术，探索AI赋能工作的无限可能。</div></li>
                </ul>
            </div>
        </div>
        <!-- Slide 9: 结束页 -->
        <div class="page">
            <div class="page-inner">
                <h1>感谢聆听！</h1>
                <p class="subtitle">Q&A</p>
            </div>
        </div>
    </main>

    <!-- 导航按钮和JS代码保持不变 -->
    <button id="prev-btn" class="nav-button hidden"><i class="fas fa-arrow-left"></i></button>
    <button id="next-btn" class="nav-button"><i class="fas fa-arrow-right"></i></button>
    <ul class="pagination"></ul>
    
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const main = document.querySelector('.main');
            const pages = document.querySelectorAll('.page');
            const paginationContainer = document.querySelector('.pagination');
            const prevBtn = document.getElementById('prev-btn');
            const nextBtn = document.getElementById('next-btn');
            
            const pageCount = pages.length;
            let activeIndex = 0;
            let isAnimating = false;

            paginationContainer.innerHTML = ''; 

            for (let i = 0; i < pageCount; i++) {
                const li = document.createElement('li');
                li.className = 'pagination-item';
                li.dataset.index = i;
                paginationContainer.appendChild(li);
            }
            const paginationItems = document.querySelectorAll('.pagination-item');

            const update = () => {
                isAnimating = true;
                main.style.transform = `translateX(-${activeIndex * 100}vw)`;
                
                pages.forEach((page, index) => {
                    page.classList.toggle('active', index === activeIndex);
                });

                paginationItems.forEach((item, index) => {
                    item.classList.toggle('active', index === activeIndex);
                });

                prevBtn.classList.toggle('hidden', activeIndex === 0);
                nextBtn.classList.toggle('hidden', activeIndex === pageCount - 1);

                setTimeout(() => {
                    isAnimating = false;
                }, 1200);
            };

            const goToPrev = () => {
                if (isAnimating || activeIndex === 0) return;
                activeIndex--;
                update();
            };

            const goToNext = () => {
                if (isAnimating || activeIndex === pageCount - 1) return;
                activeIndex++;
                update();
            };
            
            paginationItems.forEach(item => {
                item.addEventListener('click', () => {
                    if (isAnimating) return;
                    const targetIndex = parseInt(item.dataset.index);
                    if (targetIndex === activeIndex) return;
                    activeIndex = targetIndex;
                    update();
                });
            });

            document.addEventListener('keydown', (e) => {
                if (e.key === 'ArrowLeft') goToPrev();
                else if (e.key === 'ArrowRight') goToNext();
            });

            prevBtn.addEventListener('click', goToPrev);
            nextBtn.addEventListener('click', goToNext);

            update();
        });
    </script>
</body>
</html>



							<script>
                            	
        document.addEventListener('DOMContentLoaded', () => {
            const main = document.querySelector('.main');
            const pages = document.querySelectorAll('.page');
            const paginationContainer = document.querySelector('.pagination');
            const prevBtn = document.getElementById('prev-btn');
            const nextBtn = document.getElementById('next-btn');
            
            const pageCount = pages.length;
            let activeIndex = 0;
            let isAnimating = false;

            paginationContainer.innerHTML = ''; 

            for (let i = 0; i < pageCount; i++) {
                const li = document.createElement('li');
                li.className = 'pagination-item';
                li.dataset.index = i;
                paginationContainer.appendChild(li);
            }
            const paginationItems = document.querySelectorAll('.pagination-item');

            const update = () => {
                isAnimating = true;
                main.style.transform = `translateX(-${activeIndex * 100}vw)`;
                
                pages.forEach((page, index) => {
                    page.classList.toggle('active', index === activeIndex);
                });

                paginationItems.forEach((item, index) => {
                    item.classList.toggle('active', index === activeIndex);
                });

                prevBtn.classList.toggle('hidden', activeIndex === 0);
                nextBtn.classList.toggle('hidden', activeIndex === pageCount - 1);

                setTimeout(() => {
                    isAnimating = false;
                }, 1200);
            };

            const goToPrev = () => {
                if (isAnimating || activeIndex === 0) return;
                activeIndex--;
                update();
            };

            const goToNext = () => {
                if (isAnimating || activeIndex === pageCount - 1) return;
                activeIndex++;
                update();
            };
            
            paginationItems.forEach(item => {
                item.addEventListener('click', () => {
                    if (isAnimating) return;
                    const targetIndex = parseInt(item.dataset.index);
                    if (targetIndex === activeIndex) return;
                    activeIndex = targetIndex;
                    update();
                });
            });

            document.addEventListener('keydown', (e) => {
                if (e.key === 'ArrowLeft') goToPrev();
                else if (e.key === 'ArrowRight') goToNext();
            });

            prevBtn.addEventListener('click', goToPrev);
            nextBtn.addEventListener('click', goToNext);

            update();
        });
    

							</script>
                        </body>
                        </html>
                    